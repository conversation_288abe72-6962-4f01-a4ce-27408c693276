package maps

import (
	"bfmap/bfnats"
	"bfmap/bfutil"
	"bfmap/config"
	"bfmap/db"
	"bfmap/dbproto"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/nats-io/nats.go"
	"github.com/ygrpc/protodb/crud"
	"github.com/zeebo/xxh3"
	"golang.org/x/text/language"
)

const WeekInSeconds = 60 * 60 * 24 * 7

// map req block result, use in single flight request as result
type mapReqBlockResult struct {
	ImageBytes []byte
	TileInfo   *TileInfo
}

// request format: /map?token=xxxxxxx&x=123&y=456&z=7&mtype=roadmap&lang=en&provider=google&gcj02=1&sysname=trial&sysexpired=0
func MapHttpHandler(w http.ResponseWriter, r *http.Request) {
	if config.IsVerboseDebugMap {
		slog.Debug("got map request", "url", r.URL.String())
	}

	// get map request token
	token := r.URL.Query().Get("token")
	if token == "" {
		respondHttpError(w, r, "no token", http.StatusBadRequest)
		return
	}

	if IsTempMapProjectToken(token) {
		resolveTempMapProjectToken(w, r)
		return
	}

	sysName := r.URL.Query().Get("sysname")
	if sysName == "" {
		respondHttpError(w, r, "no sys name", http.StatusBadRequest)
		return
	}

	// validate token
	project, projectToken, status, err := validateMapReqToken(token, sysName)
	if err != nil {
		respondHttpError(w, r, err.Error(), status)
		return
	}
	go projectToken.AddOneUsageCount()

	mapReq, status, err := parseMapReq(r, projectToken)
	if err != nil {
		respondHttpError(w, r, err.Error(), status)
		return
	}

	if r.URL.Query().Get("sysexpired") == "1" {
		handleWithCacheOrOSMMapTile(w, r, mapReq)
		return
	}

	// block map req processing, wait for result(image bytes)
	result, err, _ := GlobalMapsManager.sfg.Do(mapReq.BlockKey(), func() (any, error) {
		tileInfo, imageBytes, err := QueryTileFromKeyValueDb(mapReq)
		if config.IsVerboseDebugMap {
			slog.Debug(
				"query tile from key-value db",
				"mapReq key",
				mapReq.Key(),
				"len",
				len(imageBytes),
				"err",
				err,
			)
		}
		needUpdateTile := false
		if err == nil {
			// check if tile need to be updated
			cacheTime := time.Unix(tileInfo.CacheTime, 0)

			if (time.Since(cacheTime) > 5*time.Hour*24*365 && mapReq.Z > 9) ||
				tileInfo.Status == 8 {
				needUpdateTile = true
			}
			if !needUpdateTile {
				return &mapReqBlockResult{ImageBytes: imageBytes, TileInfo: tileInfo}, nil
			}
		}

		// it could be not found in db or tile need to be updated
		if !needUpdateTile && config.NatsRequestMapTile {
			// not found in db, query tile from nats
			mapReqBytes, err := json.Marshal(mapReq)
			if err == nil {
				msg, err := bfnats.NatsRequest(
					mapReqNatSubject,
					mapReqBytes,
					time.Duration(config.NatsWaitTimeout)*time.Second,
				)
				if config.IsVerboseDebugMap {
					slog.Debug("send map req to nats", "msg", msg, "err", err)
				}
				if err == nil {
					tileInfo, err = SaveTileToDb(mapReq, msg.Data, false)
					if err != nil {
						slog.Warn("got tile from nats but save to db fail", "err", err)
						return &mapReqBlockResult{
							ImageBytes: msg.Data,
						}, nil
					}
					return &mapReqBlockResult{
						ImageBytes: msg.Data,
						TileInfo:   tileInfo,
					}, nil
				}
			}
		}

		// query tile from tile server
		tileInfo, imageBytes, err = QueryTileFromProvider(project.UserRid, mapReq, !needUpdateTile)
		if err != nil {
			return nil, err
		}

		return &mapReqBlockResult{ImageBytes: imageBytes, TileInfo: tileInfo}, err
	})

	if err != nil {
		respondHttpError(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	mapReqBlockResult, ok := result.(*mapReqBlockResult)
	if !ok {
		respondHttpError(w, r, "result is not *mapReqBlockResult in http handler", http.StatusInternalServerError)
		return
	}

	// respond image bytes
	respondImageBytes(w, r, mapReq, mapReqBlockResult.TileInfo, mapReqBlockResult.ImageBytes)
	// store map req access time
	GlobalMapsManager.UpdateMapCacheIndexAccessTime(mapReq)
}

func resolveTempMapProjectToken(w http.ResponseWriter, r *http.Request) {
	mapReq, status, err := parseMapReq(r, nil)
	if err != nil {
		respondHttpError(w, r, err.Error(), status)
		return
	}

	handleWithCacheOrOSMMapTile(w, r, mapReq)
}

func handleWithCacheOrOSMMapTile(w http.ResponseWriter, r *http.Request, mapReq *MapReq) {
	var imageBytes []byte
	var tileInfo *TileInfo
	var err error
	defer func() {
		if imageBytes != nil && err == nil {
			respondImageBytes(w, r, mapReq, tileInfo, imageBytes)
			return
		}

		if mapReq.Provider == int(dbproto.MapProviderEnum_ProviderOSM) &&
			mapReq.MapType == MapTypeRoadmap {
			b, err2 := ReqMapTileFromDefaultOSM(mapReq.X, mapReq.Y, mapReq.Z)
			if err2 == nil {
				respondImageBytes(w, r, mapReq, nil, b)
				return
			}

			respondHttpError(
				w,
				r,
				"failed to request map tile from osm server: "+err2.Error(),
				http.StatusInternalServerError,
			)
			return
		}

		respondHttpError(w, r, err.Error(), http.StatusNotFound)
		return
	}()

	tileInfo, imageBytes, err = QueryTileFromKeyValueDb(mapReq)
	if err == nil {
		return
	}

	// 尝试从本地目录获取瓦片（仅在sysexpired=1时启用）
	localProviderType := getLocalDirectoryProviderType(mapReq.Provider)
	if localProviderType != -1 {
		tileInfo, imageBytes, err = QueryTileFromLocalDirectory("", mapReq, localProviderType, false)
		if err == nil {
			if config.IsVerboseDebugMap {
				slog.Debug("successfully got tile from local directory",
					"mapReq", mapReq.Key(),
					"localProviderType", localProviderType)
			}
			return
		}
		if config.IsVerboseDebugMap {
			slog.Debug("failed to get tile from local directory",
				"mapReq", mapReq.Key(),
				"localProviderType", localProviderType,
				"err", err)
		}
	}

	return
}

func validateMapReqToken(
	token, sysName string,
) (project *MapProject, projectToken *MapProjectToken, status int, err error) {
	// before everything else, check token and add one usage count first
	t, err := GlobalMapsManager.GetMapProjectTokenCache(token)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil, http.StatusNotFound, errors.New("token not found")
		}
		return nil, nil, http.StatusNotFound, fmt.Errorf("get project token failed: %w", err)
	}
	if !t.IsActive() {
		return nil, nil, http.StatusForbidden, errors.New("token not active")
	}
	if t.IsExpired() {
		return nil, nil, http.StatusForbidden, errors.New("token expired")
	}

	if !t.HasSysName(sysName) {
		return nil, nil, http.StatusForbidden, errors.New("sys name not match")
	}

	p, err := GlobalMapsManager.GetMapProjectCache(t.ProjectRid)
	if err != nil {
		return nil, nil, http.StatusNotFound, fmt.Errorf("get project failed: %w", err)
	}
	if !p.IsActive() {
		return nil, nil, http.StatusForbidden, errors.New("project not active")
	}

	if p.IsQuotasOver() {
		return nil, nil, http.StatusForbidden, errors.New("project quotas over")
	}

	return p, t, http.StatusOK, nil
}

func IsMapTypeValid(mapType string) bool {
	switch mapType {
	case MapTypeRoadmap:
		return true
	case MapTypeSatellite:
		return true
	case MapTypeHybrid:
		return true
	}

	return false
}

func parseMapReq(r *http.Request, projectToken *MapProjectToken) (mapReq *MapReq, status int, err error) {
	xInt, yInt, zInt, err := parseXYZ(r)
	if err != nil {
		return nil, http.StatusBadRequest, err
	}

	mtype := r.URL.Query().Get("mtype")
	if len(mtype) == 0 {
		mtype = MapTypeRoadmap
	}

	if !IsMapTypeValid(mtype) {
		return nil, http.StatusForbidden, errors.New("invalid map type")
	}

	provider := parseProvider(r)

	if projectToken != nil {
		if provider < 0 {
			provider = projectToken.DefaultProvider(mtype)
		}

		if !projectToken.IsMapProviderAllowed(provider) {
			return nil, http.StatusForbidden, errors.New("provider not allowed")
		}
	}

	if provider == int(dbproto.MapProviderEnum_ProviderOSM) &&
		mtype != MapTypeRoadmap {
		return nil, http.StatusNotAcceptable, errors.New("OSM map type should be roadmap")
	}

	gcj02Str := r.URL.Query().Get("gcj02")
	gcj02, _ := strconv.Atoi(gcj02Str)
	if provider != int(dbproto.MapProviderEnum_ProviderGoogle) {
		gcj02 = 0
	}

	imageFormat := TileImageFormatPng
	if mtype == MapTypeSatellite || mtype == MapTypeHybrid {
		imageFormat = TileImageFormatJpg
	}

	if provider == int(dbproto.MapProviderEnum_ProviderOSM) {
		imageFormat = TileImageFormatPng
	}

	lang := r.URL.Query().Get("lang")
	langTag, err := language.All.Parse(lang)
	if err != nil {
		return nil, 0, fmt.Errorf("invalid language: %w", err)
	}
	lang = langTag.String()

	region := "US"
	langRegion, _ := langTag.Region()
	regionStr := langRegion.String()
	if regionStr != "ZZ" {
		region = regionStr
	}

	if len(lang) == 0 {
		lang = "en"
	}

	if provider == int(dbproto.MapProviderEnum_ProviderOSM) || mtype == MapTypeSatellite {
		lang = ""
	}

	if provider == int(dbproto.MapProviderEnum_ProviderTianditu) {
		lang = "zh-CN"
	}

	mapReq = &MapReq{
		X:           xInt,
		Y:           yInt,
		Z:           zInt,
		EnableGcj02: gcj02,
		MapType:     mtype,
		Lang:        lang,
		Provider:    provider,
		ImageFormat: imageFormat,
		Region:      region,
	}
	mapReq.CalcParams()
	return mapReq, http.StatusOK, nil
}

func parseXYZ(r *http.Request) (xInt int, yInt int, zInt int, err error) {
	x := r.URL.Query().Get("x")
	if len(x) == 0 {
		err = errors.New("x is empty")
		return
	}
	xInt, err = strconv.Atoi(x)
	if err != nil || xInt < 0 {
		err = fmt.Errorf("invalid x: %w", err)
		return
	}
	y := r.URL.Query().Get("y")
	if len(y) == 0 {
		err = errors.New("y is empty")
		return
	}
	yInt, err = strconv.Atoi(y)
	if err != nil || yInt < 0 {
		err = fmt.Errorf("invalid y: %w", err)
		return
	}
	z := r.URL.Query().Get("z")
	if len(z) == 0 {
		err = errors.New("z is empty")
		return
	}
	zInt, err = strconv.Atoi(z)
	if err != nil {
		err = fmt.Errorf("invalid z: %w", err)
		return
	}
	if zInt < 0 {
		err = errors.New("invalid z: must be non-negative")
		return
	}

	return
}

func parseProvider(r *http.Request) (provider int) {
	mapProvider := r.URL.Query().Get("provider")
	switch mapProvider {
	case MapProviderGoogle:
		provider = int(dbproto.MapProviderEnum_ProviderGoogle)
	case MapProviderTianDiTu:
		provider = int(dbproto.MapProviderEnum_ProviderTianditu)
	case MapProviderOSM:
		provider = int(dbproto.MapProviderEnum_ProviderOSM)
	default:
		provider = -1
	}

	return
}

func respondHttpError(w http.ResponseWriter, r *http.Request, message string, status int) {
	if r.Context().Err() != nil {
		if config.IsVerboseDebugMap {
			slog.Debug(
				"respond error to map req but context is canceled, ignore",
				"status",
				status,
				"err",
				message,
				"url",
				r.URL.String(),
			)
		}
		return
	}
	w.Header().Set("Content-Type", "text/plain; charset=utf-8")
	w.WriteHeader(status)
	_, err := w.Write([]byte(message))
	if config.IsVerboseDebugMap {
		slog.Debug("respond error to map req", "status", status, "err", message, "url", r.URL.String())
	}
	if err != nil {
		slog.Warn("respond fail", "status", status, "err", err, "url", r.URL.String())
	}
}

func respondImageBytes(w http.ResponseWriter, r *http.Request, mapReq *MapReq, tileInfo *TileInfo, imageByte []byte) {
	if r.Context().Err() != nil {
		if config.IsVerboseDebugMap {
			slog.Debug(
				"respond image to map req but context is canceled, ignore",
				"len",
				len(imageByte),
				"url",
				r.URL.String(),
			)
		}
		return
	}
	// check if client has cached content
	if tileInfo != nil && r.Header.Get("Cache-Control") != "no-cache" {
		if strings.Trim(r.Header.Get("If-None-Match"), `"`) == tileInfo.Hash {
			w.WriteHeader(http.StatusNotModified)
			return
		}

		parse, err := time.Parse(http.TimeFormat, r.Header.Get("If-Modified-Since"))
		if err == nil && parse.UTC().Unix() >= tileInfo.CacheTime {
			w.WriteHeader(http.StatusNotModified)
			return
		}
	}

	var contentType string
	switch mapReq.MapType {
	case MapTypeRoadmap:
		contentType = "image/png"
	case MapTypeSatellite, MapTypeHybrid:
		contentType = "image/jpeg"
	default:
		contentType = "application/octet-stream"
	}

	header := w.Header()
	header.Set("Content-Type", contentType)
	header.Set("Content-Length", strconv.Itoa(len(imageByte)))

	// set http cache headers
	if tileInfo != nil {
		header.Set(MapCacheTimeHeaderName, strconv.FormatInt(tileInfo.CacheTime, 10))
		header.Set("Cache-Control", "no-cache") //always check for content updates while reusing stored content
		header.Set("ETag", fmt.Sprintf(`"%s"`, tileInfo.Hash))
		header.Set("Last-Modified", time.Unix(tileInfo.CacheTime, 0).Format(http.TimeFormat))
		//set tile status
		header.Set(MapTileStatusHeaderName, strconv.Itoa(tileInfo.Status))
	}

	switch mapReq.Provider {
	case int(dbproto.MapProviderEnum_ProviderGoogle):
		header.Set(MapProviderHeaderName, MapProviderGoogle)
	case int(dbproto.MapProviderEnum_ProviderTianditu):
		header.Set(MapProviderHeaderName, MapProviderTianDiTu)
	case int(dbproto.MapProviderEnum_ProviderOSM):
		header.Set(MapProviderHeaderName, MapProviderOSM)
	}

	w.WriteHeader(http.StatusOK)

	_, err := w.Write(imageByte)
	if config.IsVerboseDebugMap {
		slog.Debug("respond image bytes", "len", len(imageByte), "Content-Type", contentType, "url", r.URL.String())
	}
	if err != nil {
		slog.Error("respond image bytes fail", "err", err)
	}
}

func QueryTileIndexFromDb(mapReq *MapReq) (MapTileCacheIndex, error) {
	var index MapTileCacheIndex
	var err error
	switch mapReq.MapType {
	case MapTypeRoadmap:
		index, err = GetDbMapCacheRoadmapIndex(
			mapReq.Provider, mapReq.Lang, mapReq.X, mapReq.Y, mapReq.Z, mapReq.ImageFormatInt, mapReq.EnableGcj02)
	case MapTypeSatellite:
		index, err = GetDbMapCacheSatelliteIndex(
			mapReq.Provider, mapReq.X, mapReq.Y, mapReq.Z, mapReq.ImageFormatInt, mapReq.EnableGcj02)
	case MapTypeHybrid:
		index, err = GetDbMapCacheHybridIndex(
			mapReq.Provider, mapReq.Lang, mapReq.X, mapReq.Y, mapReq.Z, mapReq.ImageFormatInt, mapReq.EnableGcj02)
	default:
		err = errors.New("unknown map type")
	}

	return index, err
}

func QueryTileFromProvider(
	userRid string,
	mapReq *MapReq,
	needCreateNewIndex bool,
) (tileInfo *TileInfo, imageBytes []byte, err error) {
	defer func() {
		if err != nil && mapReq.Provider == int(dbproto.MapProviderEnum_ProviderOSM) {
			imageBytes, err = ReqMapTileFromDefaultOSM(mapReq.X, mapReq.Y, mapReq.Z)
			if config.IsVerboseDebugMap {
				slog.Debug(
					"QueryTileFromProvider fallback to req map tile from default osm",
					"map req key",
					mapReq.Key(),
					"err",
					err,
				)
			}
			if err != nil {
				return
			}
			tileInfo, err = SaveTileToDb(mapReq, imageBytes, needCreateNewIndex)
			if err != nil {
				slog.Warn("save tile to db fail", "map req key", mapReq.Key(), "err", err)
				return
			}
		}
	}()
	var providerTokens []*MapProviderToken
	providerTokens, err = GetMapProviderTokensWithAdminFallback(userRid, mapReq.Provider)
	if err != nil {
		// if map provider is osm but no token, try to request from default osm without token
		return nil, nil, err
	}

	index, err := chooseMapProviderTokenFromSlice(providerTokens)
	if err != nil {
		return nil, nil, errors.New("no valid provider token")
	}

	for ; index < len(providerTokens); index++ {
		providerToken := providerTokens[index]
		if !providerToken.IsValid() {
			continue
		}
		if providerToken.MinZoom > 0 && mapReq.Z < providerToken.MinZoom {
			continue
		}

		if providerToken.MaxZoom > 0 && mapReq.Z > providerToken.MaxZoom {
			continue
		}
		tileInfo, imageBytes, err = ReqMapFromProviderWithToken(providerToken, mapReq, needCreateNewIndex)
		if config.IsVerboseDebugMap {
			slog.Debug("query tile from provider",
				"userRid", userRid,
				"err", err)
		}
		if err == nil {
			return tileInfo, imageBytes, nil
		}
		// Mark token as failed for this request
		GlobalMapsManager.SetMapProviderTokenLastFailTime(providerToken.TokenRid, bfutil.CurrentUTCTime())
	}

	return nil, nil, errors.New("failed to request with all provider tokens and admin fallback")
}

func ReqMapFromProviderWithToken(
	providerToken *MapProviderToken,
	mapReq *MapReq,
	needCreateNewIndex bool,
) (*TileInfo, []byte, error) {
	if providerToken.IsQuotasOver() {
		return nil, nil, errors.New("quotas over")
	}
	if providerToken.IsExpired() {
		return nil, nil, errors.New("token is expired")
	}
	if !providerToken.IsActive() {
		return nil, nil, errors.New("token is disabled")
	}

	var tileInfo *TileInfo
	var imageBytes []byte
	var err error
	switch providerToken.Provider {
	case dbproto.MapProviderEnum_ProviderGoogle:
		if providerToken.GoogleMapApi == dbproto.GoogleMapApiEnum_GoogleMapApiStatic {
			imageBytes, err = ReqGoogleMapStaticWithGcj02Convert(providerToken, mapReq)
		} else {
			imageBytes, err = ReqGoogleMapTileWithGcj02Convert(providerToken, mapReq)
		}
	case dbproto.MapProviderEnum_ProviderTianditu:
		imageBytes, err = providerToken.ReqTiandutuMapTile(mapReq)
	case dbproto.MapProviderEnum_ProviderOSM:
		imageBytes, err = providerToken.ReqOSMMapTile(mapReq)

	// 新增本地目录provider支持
	case dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
		 dbproto.MapProviderEnum_ProviderTiandituLocalDirectory,
		 dbproto.MapProviderEnum_ProviderOSMLocalDirectory:
		imageBytes, err = readLocalDirectoryFile(providerToken, mapReq)

	default:
		return nil, nil, errors.New("not support provider")
	}
	if err != nil {
		return nil, nil, fmt.Errorf("req map from provider fail: %w", err)
	}

	if len(imageBytes) == 0 {
		return nil, nil, errors.New("image bytes is empty")
	}

	tileInfo, err = SaveTileToDb(mapReq, imageBytes, needCreateNewIndex)
	if err != nil {
		slog.Warn("ReqMapFromProviderWithToken SaveTileToDb fail", "req", mapReq.Key(), "err", err)
	}

	return tileInfo, imageBytes, nil
}

// save tile to db,needCreate means index not exist,create it.
func SaveTileToDb(mapReq *MapReq, imageBytes []byte, needCreateNewIndex bool) (tileInfo *TileInfo, err error) {
	// calculate hash
	u := xxh3.Hash128(imageBytes).Bytes()
	saveHash := base64.StdEncoding.EncodeToString(u[:])
	cacheTime := bfutil.CurrentUTCTime().Unix()

	// save index to db
	var oldIndex MapTileCacheIndex
	var isSaved bool
	switch mapReq.MapType {
	case MapTypeRoadmap:
		oldIndex, isSaved, err = saveDbMapCacheRoadmapIndex(mapReq, saveHash, cacheTime, needCreateNewIndex)
	case MapTypeSatellite:
		oldIndex, isSaved, err = saveDbMapCacheSatelliteIndex(mapReq, saveHash, cacheTime, needCreateNewIndex)
	case MapTypeHybrid:
		oldIndex, isSaved, err = saveDbMapCacheHybridIndex(mapReq, saveHash, cacheTime, needCreateNewIndex)
	default:
		err = errors.New("unknown map type")
	}
	if config.IsVerboseDebugMap {
		slog.Debug("save tile index to db", "hash", saveHash, "is saved", isSaved, "map req key", mapReq.Key())
	}
	if err != nil {
		return nil, err
	}

	if !isSaved {
		// no save in db, no need save to key-value db
		return &TileInfo{
			Hash:      oldIndex.GetTileHash(),
			CacheTime: oldIndex.GetCacheTime(),
			Status:    int(oldIndex.GetStatus()),
		}, nil
	}

	tileInfo = &TileInfo{Hash: saveHash, CacheTime: cacheTime, Status: 1}
	err = SaveTileToKeyValueDb(mapReq.Key(), tileInfo, imageBytes)
	if config.IsVerboseDebugMap {
		slog.Debug(
			"save tile to key-value db",
			"hash",
			saveHash,
			"size",
			len(imageBytes),
			"map req key",
			mapReq.Key(),
			"err",
			err,
		)
	}

	if oldIndex == nil {
		return tileInfo, err
	}

	oldIndexHash := oldIndex.GetTileHash()
	if oldIndexHash != "" && oldIndexHash != saveHash {
		err = db.KeyValueDbDelete([]byte(oldIndexHash))
		if config.IsVerboseDebugMap {
			slog.Debug(
				"delete old tile to key-value db",
				"oldHash",
				oldIndexHash,
				"map req key",
				mapReq.Key(),
				"err",
				err,
			)
		}
	}

	return tileInfo, err
}

func saveDbMapCacheHybridIndex(
	mapReq *MapReq,
	newHash string,
	cacheTime int64,
	needCreateNewIndex bool,
) (oldIndex *dbproto.DbMapCacheHybridIndex, isSaved bool, err error) {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return nil, false, err
	}

	index := &dbproto.DbMapCacheHybridIndex{
		Rid:             bfutil.UuidV7(),
		Provider:        dbproto.MapProviderEnum(mapReq.Provider),
		Language:        mapReq.Lang,
		TileX:           int32(mapReq.X),
		TileY:           int32(mapReq.Y),
		TileZ:           int32(mapReq.Z),
		CacheTime:       cacheTime,
		AccessTime:      cacheTime,
		TileImageFormat: int32(mapReq.ImageFormatInt),
		TileHash:        newHash,
		Gcj02:           int32(mapReq.EnableGcj02),
		Status:          1,
	}

	if needCreateNewIndex {
		_, err = crud.DbInsert(dbConn.WriteDB, index, 0, "")
		if err == nil {
			return nil, true, nil
		}
	}

	keyColumns := []string{"Provider", "Language", "TileX", "TileY", "TileZ", "TileImageFormat", "Gcj02"}
	oldMsg, err := crud.DbSelectOne(dbConn.ReadDB, index, keyColumns, nil, "", false)
	if err != nil {
		return nil, false, fmt.Errorf("select old DbMapCacheHybridIndex fail: %w", err)
	}
	oldIndex = oldMsg.(*dbproto.DbMapCacheHybridIndex)

	if oldIndex.Status != 8 &&
		(oldIndex.TileHash == newHash || bfutil.CurrentUTCTimeUnix()-oldIndex.CacheTime < WeekInSeconds) {
		// same hash or old hash cache in week, do nothing and return
		return oldIndex, false, nil
	}

	index.Rid = oldIndex.Rid
	_, err = crud.DbUpdatePartial(dbConn.WriteDB, index, []string{"CacheTime", "AccessTime", "TileHash", "Status"}, "")
	return oldIndex, true, err
}

func saveDbMapCacheSatelliteIndex(
	mapReq *MapReq,
	newHash string,
	cacheTime int64,
	needCreateNewIndex bool,
) (oldIndex *dbproto.DbMapCacheSatelliteIndex, isSaved bool, err error) {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return nil, false, err
	}

	index := &dbproto.DbMapCacheSatelliteIndex{
		Rid:             bfutil.UuidV7(),
		Provider:        dbproto.MapProviderEnum(mapReq.Provider),
		TileX:           int32(mapReq.X),
		TileY:           int32(mapReq.Y),
		TileZ:           int32(mapReq.Z),
		CacheTime:       cacheTime,
		AccessTime:      cacheTime,
		TileImageFormat: int32(mapReq.ImageFormatInt),
		TileHash:        newHash,
		Gcj02:           int32(mapReq.EnableGcj02),
		Status:          1,
	}

	if needCreateNewIndex {
		_, err = crud.DbInsert(dbConn.WriteDB, index, 0, "")
		if err == nil {
			return nil, true, nil
		}
	}

	keyColumns := []string{"Provider", "TileX", "TileY", "TileZ", "TileImageFormat", "Gcj02"}
	oldMsg, err := crud.DbSelectOne(dbConn.ReadDB, index, keyColumns, nil, "", false)
	if err != nil {
		return nil, false, fmt.Errorf("select old DbMapCacheSatelliteIndex fail: %w", err)
	}
	oldIndex = oldMsg.(*dbproto.DbMapCacheSatelliteIndex)

	if oldIndex.Status != 8 &&
		(oldIndex.TileHash == newHash || bfutil.CurrentUTCTimeUnix()-oldIndex.CacheTime < WeekInSeconds) {
		// same hash or old hash cache in week, do nothing and return
		return oldIndex, false, nil
	}

	index.Rid = oldIndex.Rid
	_, err = crud.DbUpdatePartial(dbConn.WriteDB, index, []string{"CacheTime", "AccessTime", "TileHash", "Status"}, "")
	return oldIndex, true, err
}

// if needCreateNewIndex is true, create new index,
// else update old index, and return old index hash.
// may not save index to db if hash is same and tile is cached in week, isSaved is false
func saveDbMapCacheRoadmapIndex(
	mapReq *MapReq,
	newHash string,
	cacheTime int64,
	needCreateNewIndex bool,
) (oldIndex *dbproto.DbMapCacheRoadmapIndex, isSaved bool, err error) {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return nil, false, err
	}

	index := &dbproto.DbMapCacheRoadmapIndex{
		Rid:             bfutil.UuidV7(),
		Provider:        dbproto.MapProviderEnum(mapReq.Provider),
		Language:        mapReq.Lang,
		TileX:           int32(mapReq.X),
		TileY:           int32(mapReq.Y),
		TileZ:           int32(mapReq.Z),
		CacheTime:       cacheTime,
		AccessTime:      cacheTime,
		TileImageFormat: int32(mapReq.ImageFormatInt),
		TileHash:        newHash,
		Gcj02:           int32(mapReq.EnableGcj02),
		Status:          1,
	}

	if needCreateNewIndex {
		_, err = crud.DbInsert(dbConn.WriteDB, index, 0, "")
		if err == nil {
			return nil, true, nil
		}
	}

	keyColumns := []string{"Provider", "Language", "TileX", "TileY", "TileZ", "TileImageFormat", "Gcj02"}
	oldMsg, err := crud.DbSelectOne(dbConn.ReadDB, index, keyColumns, nil, "", false)
	if err != nil {
		return nil, false, fmt.Errorf("select old DbMapCacheRoadmapIndex fail: %w", err)
	}
	oldIndex = oldMsg.(*dbproto.DbMapCacheRoadmapIndex)

	if oldIndex.Status != 8 &&
		(oldIndex.TileHash == newHash || bfutil.CurrentUTCTimeUnix()-oldIndex.CacheTime < WeekInSeconds) {
		// same hash or old hash cache in week, do nothing and return
		return oldIndex, false, nil
	}

	index.Rid = oldIndex.Rid
	_, err = crud.DbUpdatePartial(dbConn.WriteDB, index, []string{"CacheTime", "AccessTime", "TileHash", "Status"}, "")
	return oldIndex, true, err
}

func natsHandlerForMapReq(msg *nats.Msg) {
	if msg.Header.Get(bfnats.NatsClientIDHeader) == bfnats.GetClientID() {
		return
	}

	mapReq := &MapReq{}
	err := json.Unmarshal(msg.Data, mapReq)
	if err != nil {
		slog.Warn("natsHandlerForMapReq unmarshal fail", "err", err, "msgData", string(msg.Data))
		return
	}

	if config.IsVerboseDebugMap {
		slog.Debug("natsHandlerForMapReq", "mapReq", mapReq)
	}

	_, imageBytes, err := QueryTileFromKeyValueDb(mapReq)
	if err != nil {
		// 尝试从本地目录获取瓦片
		localProviderType := getLocalDirectoryProviderType(mapReq.Provider)
		if localProviderType != -1 {
			_, imageBytes, err = QueryTileFromLocalDirectory("", mapReq, localProviderType, false)
			if err == nil {
				if config.IsVerboseDebugMap {
					slog.Debug("natsHandlerForMapReq successfully got tile from local directory",
						"mapReq", mapReq.Key(),
						"localProviderType", localProviderType)
				}
				// 成功从本地目录获取，继续响应
			} else {
				if config.IsVerboseDebugMap {
					slog.Debug("natsHandlerForMapReq failed to get tile from local directory",
						"mapReq", mapReq.Key(),
						"localProviderType", localProviderType,
						"err", err)
				}
				// 本地目录也失败，记录日志并返回
				if config.IsVerboseDebugMap {
					slog.Debug("natsHandlerForMapReq query tile from db fail", "err", err, "msgData", string(msg.Data))
				}
				return
			}
		} else {
			if config.IsVerboseDebugMap {
				slog.Debug("natsHandlerForMapReq query tile from db fail", "err", err, "msgData", string(msg.Data))
			}
			return
		}
	}

	err = msg.Respond(imageBytes)
	if err != nil {
		if config.IsVerboseDebugMap {
			slog.Warn("natsHandlerForMapReq respond fail", "err", err, "msgData", string(msg.Data))
		}
		return
	}
}

func MapSubscribeNats() (*nats.Subscription, error) {
	return bfnats.NatsSubscribe(mapReqNatSubject, natsHandlerForMapReq)
}
