package maps

import (
	"bfmap/config"
	"bfmap/dbproto"
	"fmt"
	"log/slog"
	"os"

	"golang.org/x/text/language"
)

// normalizeLanguageCode 标准化语言代码为BCP 47格式
func normalizeLanguageCode(lang string) (string, error) {
	if lang == "" {
		return "", nil
	}

	// 解析语言标签
	tag, err := language.Parse(lang)
	if err != nil {
		return "", fmt.Errorf("invalid language code: %w", err)
	}

	// 标准化为BCP 47格式
	return tag.String(), nil
}

// normalizeChinese 标准化中文语言代码
// 使用标准库处理中文语言变体，将各种中文变体归一化为标准格式
func normalizeChinese(lang string) string {
	// 先使用标准库解析语言标签
	tag, err := language.Parse(lang)
	if err != nil {
		// 如果解析失败，使用简单的映射作为fallback
		switch lang {
		case "zh", "zh_CN", "zh-CN":
			return "zh-CN"
		default:
			return lang
		}
	}

	// 对于中文，确保使用zh-CN格式
	if base, _ := tag.Base(); base.String() == "zh" {
		// 对于中文，统一返回zh-CN格式
		// 这里我们明确地构造zh-CN，而不依赖于原始输入的格式
		return "zh-CN"
	}

	// 非中文语言，返回标准化格式
	return tag.String()
}

// matchLanguage 执行语言匹配，支持精确匹配和语言族匹配
func matchLanguage(requestLang, tokenLang string) bool {
	// 标准化语言代码
	reqLang, err := normalizeLanguageCode(requestLang)
	if err != nil {
		if config.IsVerboseDebugMap {
			slog.Warn("failed to normalize request language code",
				"requestLang", requestLang,
				"err", err)
		}
		return false
	}

	tokLang, err := normalizeLanguageCode(tokenLang)
	if err != nil {
		if config.IsVerboseDebugMap {
			slog.Warn("failed to normalize token language code",
				"tokenLang", tokenLang,
				"err", err)
		}
		return false
	}

	// 精确匹配
	if reqLang == tokLang {
		return true
	}

	// 语言族匹配
	reqTag, err := language.Parse(reqLang)
	if err != nil {
		return false
	}

	tokTag, err := language.Parse(tokLang)
	if err != nil {
		return false
	}

	// 比较语言族（去除地区信息）
	reqBase, _ := reqTag.Base()
	tokBase, _ := tokTag.Base()

	return reqBase == tokBase
}

// matchTiandituLanguage 天地图特定的语言匹配逻辑
// 仅支持中文，使用标准库处理语言变体
func matchTiandituLanguage(requestLang, tokenLang string) bool {
	normalizedReqLang := normalizeChinese(requestLang)
	normalizedTokenLang := normalizeChinese(tokenLang)

	return normalizedReqLang == "zh-CN" && normalizedTokenLang == "zh-CN"
}

// isLanguageCompatible 检查请求语言与token语言是否兼容
// 根据不同的provider类型和地图类型执行相应的匹配逻辑
func isLanguageCompatible(requestLang, tokenLang string, providerType dbproto.MapProviderEnum, mapType string) bool {
	// 向后兼容：空token语言匹配所有请求
	if tokenLang == "" {
		return true
	}

	// 卫星图跳过语言检查，因为卫星图通常不包含文字
	if mapType == MapTypeSatellite {
		return true
	}

	// 根据provider类型执行特定匹配逻辑
	switch providerType {
	case dbproto.MapProviderEnum_ProviderGoogleLocalDirectory:
		// Google支持多语言匹配
		return matchLanguage(requestLang, tokenLang)

	case dbproto.MapProviderEnum_ProviderTiandituLocalDirectory:
		// 天地图仅支持中文
		return matchTiandituLanguage(requestLang, tokenLang)

	case dbproto.MapProviderEnum_ProviderOSMLocalDirectory:
		// OSM跳过语言匹配，语言由地理位置决定
		return true

	default:
		// 未知provider类型，默认不兼容
		if config.IsVerboseDebugMap {
			slog.Warn("unknown provider type for language compatibility check",
				"providerType", providerType)
		}
		return false
	}
}

// getLocalDirectoryProviderType 将在线provider类型映射为对应的本地目录provider类型
func getLocalDirectoryProviderType(onlineProviderType int) int {
	switch onlineProviderType {
	case int(dbproto.MapProviderEnum_ProviderGoogle):
		return int(dbproto.MapProviderEnum_ProviderGoogleLocalDirectory)
	case int(dbproto.MapProviderEnum_ProviderTianditu):
		return int(dbproto.MapProviderEnum_ProviderTiandituLocalDirectory)
	case int(dbproto.MapProviderEnum_ProviderOSM):
		return int(dbproto.MapProviderEnum_ProviderOSMLocalDirectory)
	default:
		// 不支持的provider类型
		return -1
	}
}

// filterCompatibleTokens 过滤出语言兼容的provider tokens
// 注意：由于proto生成问题，暂时使用Setting字段存储语言信息
func filterCompatibleTokens(tokens []*MapProviderToken, mapReq *MapReq) []*MapProviderToken {
	var compatibleTokens []*MapProviderToken

	for _, token := range tokens {
		// 检查token基本有效性
		if !token.IsValid() {
			continue
		}

		// 检查zoom范围
		if token.MinZoom > 0 && mapReq.Z < token.MinZoom {
			continue
		}
		if token.MaxZoom > 0 && mapReq.Z > token.MaxZoom {
			continue
		}

		// 使用Language字段
		tokenLanguage := token.Language

		// 检查语言兼容性
		if isLanguageCompatible(mapReq.Lang, tokenLanguage, token.Provider, mapReq.MapType) {
			compatibleTokens = append(compatibleTokens, token)

			if config.IsVerboseDebugMap {
				slog.Debug("found language-compatible token",
					"tokenRid", token.TokenRid,
					"requestLang", mapReq.Lang,
					"tokenLang", tokenLanguage,
					"provider", token.Provider,
					"mapType", mapReq.MapType)
			}
		} else {
			if config.IsVerboseDebugMap {
				slog.Debug("token language not compatible",
					"tokenRid", token.TokenRid,
					"requestLang", mapReq.Lang,
					"tokenLang", tokenLanguage,
					"provider", token.Provider,
					"mapType", mapReq.MapType)
			}
		}
	}

	return compatibleTokens
}

// buildLocalFilePath 构建本地文件路径
func buildLocalFilePath(token *MapProviderToken, mapReq *MapReq) string {
	baseUrl := token.BaseUrl
	if baseUrl == "" {
		return ""
	}

	// 移除末尾的斜杠
	if baseUrl[len(baseUrl)-1] == '/' {
		baseUrl = baseUrl[:len(baseUrl)-1]
	}

	// 确定文件扩展名
	ext := "png"
	if mapReq.MapType == MapTypeSatellite || mapReq.MapType == MapTypeHybrid {
		ext = "jpg"
	}

	// 构建文件路径：{BaseUrl}/{mapType}/{z}/{x}/{y}.{ext}
	return fmt.Sprintf("%s/%s/%d/%d/%d.%s",
		baseUrl, mapReq.MapType, mapReq.Z, mapReq.X, mapReq.Y, ext)
}

// QueryTileFromLocalDirectory 从本地目录查询地图瓦片
func QueryTileFromLocalDirectory(
	userRid string,
	mapReq *MapReq,
	localProviderType int,
	needCreateNewIndex bool,
) (tileInfo *TileInfo, imageBytes []byte, err error) {
	// 获取本地目录provider的tokens
	providerTokens, err := GlobalMapsManager.GetMapProviderTokens(userRid, localProviderType)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get local directory provider tokens: %w", err)
	}

	if len(providerTokens) == 0 {
		return nil, nil, fmt.Errorf("no local directory provider tokens found for provider type %d", localProviderType)
	}

	// 过滤出语言兼容的tokens
	compatibleTokens := filterCompatibleTokens(providerTokens, mapReq)
	if len(compatibleTokens) == 0 {
		return nil, nil, fmt.Errorf("no language-compatible local directory provider token found")
	}

	// 尝试从兼容的tokens中读取文件
	for _, token := range compatibleTokens {
		filePath := buildLocalFilePath(token, mapReq)
		if filePath == "" {
			continue
		}

		// 尝试读取本地文件
		imageBytes, err = readLocalDirectoryFile(token, mapReq)
		if err != nil {
			if config.IsVerboseDebugMap {
				slog.Debug("failed to read local directory file",
					"filePath", filePath,
					"tokenRid", token.TokenRid,
					"err", err)
			}
			continue
		}

		// 成功读取文件
		if config.IsVerboseDebugMap {
			slog.Debug("successfully read local directory file",
				"filePath", filePath,
				"tokenRid", token.TokenRid,
				"fileSize", len(imageBytes))
		}

		// 保存到数据库并创建TileInfo
		tileInfo, err = SaveTileToDb(mapReq, imageBytes, needCreateNewIndex)
		if err != nil {
			slog.Warn("QueryTileFromLocalDirectory SaveTileToDb fail", "req", mapReq.Key(), "err", err)
			// 即使保存失败，也返回图片数据
			tileInfo = &TileInfo{
				Hash:      "",
				CacheTime: 0,
				Status:    1,
			}
		}

		return tileInfo, imageBytes, nil
	}

	return nil, nil, fmt.Errorf("no local directory file found for request")
}

// readLocalDirectoryFile 从本地目录读取文件
func readLocalDirectoryFile(token *MapProviderToken, mapReq *MapReq) ([]byte, error) {
	filePath := buildLocalFilePath(token, mapReq)
	if filePath == "" {
		return nil, fmt.Errorf("invalid file path")
	}

	// 读取文件
	imageBytes, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file %s: %w", filePath, err)
	}

	if len(imageBytes) == 0 {
		return nil, fmt.Errorf("file %s is empty", filePath)
	}

	return imageBytes, nil
}
