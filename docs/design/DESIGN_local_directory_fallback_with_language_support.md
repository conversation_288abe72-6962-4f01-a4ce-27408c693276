# 本地目录Fallback机制与语言支持功能设计文档

## 设计概述

本文档详细描述了本地目录fallback机制与语言支持功能的系统架构设计、模块交互关系和实现细节。该设计基于现有的地图服务架构，通过添加本地目录fallback层和语言匹配机制，提升系统的可靠性和国际化支持能力。

## 系统架构设计

### 整体架构图

```mermaid
graph TB
    Client[客户端请求] --> MapHandler[MapHttpHandler]
    MapHandler --> Cache[缓存查询<br/>QueryTileFromKeyValueDb]

    Cache -->|成功| Response[返回瓦片]
    Cache -->|失败/需更新| LocalDir[本地目录Fallback<br/>QueryTileFromLocalDirectory]

    LocalDir --> LangMatch[语言匹配<br/>isLanguageCompatible]
    LangMatch -->|匹配成功| FileRead[本地文件读取<br/>readLocalDirectoryFile]
    LangMatch -->|匹配失败| NATS[NATS请求]

    FileRead -->|成功| SaveDB[保存到数据库<br/>SaveTileToDb]
    FileRead -->|失败| NATS

    NATS -->|成功| SaveDB
    NATS -->|失败| Online[在线Provider<br/>QueryTileFromProvider]

    Online --> SaveDB
    SaveDB --> Response

    subgraph "语言匹配模块"
        LangMatch --> NormLang[语言标准化<br/>normalizeLanguageCode]
        LangMatch --> ProviderRule[Provider特定规则]
        ProviderRule --> GoogleRule[Google: 多语言支持]
        ProviderRule --> TiandituRule[天地图: 仅zh-CN]
        ProviderRule --> OSMRule[OSM: 跳过语言检查]
    end

    subgraph "本地目录模块"
        LocalDir --> ProviderMap[Provider类型映射<br/>getLocalDirectoryProviderType]
        LocalDir --> TokenFilter[Token过滤<br/>语言兼容性检查]
        TokenFilter --> FileRead
    end
```

### 核心模块设计

#### 1. 语言匹配模块 (Language Matching Module)

**职责**：
- 语言代码标准化处理
- 不同Provider的语言匹配规则实现
- 向后兼容性处理

**核心组件**：
```mermaid
classDiagram
    class LanguageMatcher {
        +normalizeLanguageCode(lang string) (string, error)
        +isLanguageCompatible(requestLang, tokenLang string, provider, mapType) bool
        +matchLanguage(requestLang, tokenLang string) bool
        +normalizeChinese(lang string) string
        +matchTiandituLanguage(requestLang, tokenLang string) bool
    }

    class ProviderLanguageRule {
        <<interface>>
        +isCompatible(requestLang, tokenLang, mapType string) bool
    }

    class GoogleLanguageRule {
        +isCompatible(requestLang, tokenLang, mapType string) bool
    }

    class TiandituLanguageRule {
        +isCompatible(requestLang, tokenLang, mapType string) bool
    }

    class OSMLanguageRule {
        +isCompatible(requestLang, tokenLang, mapType string) bool
    }

    LanguageMatcher --> ProviderLanguageRule
    ProviderLanguageRule <|-- GoogleLanguageRule
    ProviderLanguageRule <|-- TiandituLanguageRule
    ProviderLanguageRule <|-- OSMLanguageRule
```

#### 2. 本地目录访问模块 (Local Directory Access Module)

**职责**：
- Provider类型映射
- 本地文件路径构建
- 文件读取和错误处理

**核心组件**：
```mermaid
classDiagram
    class LocalDirectoryManager {
        +getLocalDirectoryProviderType(onlineType int) int
        +buildLocalFilePath(token *MapProviderToken, mapReq *MapReq) string
        +readLocalDirectoryFile(token *MapProviderToken, mapReq *MapReq) ([]byte, error)
    }

    class FilePathBuilder {
        +buildPath(baseUrl, mapType string, x, y, z int) string
        +getFileExtension(mapType string) string
    }

    class FileReader {
        +readFile(filePath string) ([]byte, error)
        +validateFile(filePath string) error
    }

    LocalDirectoryManager --> FilePathBuilder
    LocalDirectoryManager --> FileReader
```

#### 3. Token过滤模块 (Token Filtering Module)

**职责**：
- 基于语言兼容性过滤Provider Token
- Token有效性验证
- 优先级排序

**核心组件**：
```mermaid
classDiagram
    class TokenFilter {
        +filterCompatibleTokens(tokens []*MapProviderToken, mapReq *MapReq) []*MapProviderToken
        +validateToken(token *MapProviderToken) bool
        +sortTokensByPriority(tokens []*MapProviderToken) []*MapProviderToken
    }

    class TokenValidator {
        +isValid(token *MapProviderToken) bool
        +checkZoomRange(token *MapProviderToken, zoom int) bool
        +checkExpiration(token *MapProviderToken) bool
    }

    TokenFilter --> TokenValidator
    TokenFilter --> LanguageMatcher
```

## 数据流设计

### 主要数据流图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Handler as MapHttpHandler
    participant Cache as 缓存层
    participant LocalDir as 本地目录模块
    participant LangMatch as 语言匹配模块
    participant FileReader as 文件读取器
    participant NATS as NATS服务
    participant Online as 在线Provider

    Client->>Handler: HTTP请求 (x,y,z,lang,provider)
    Handler->>Cache: QueryTileFromKeyValueDb(mapReq)

    alt 缓存命中且有效
        Cache-->>Handler: 返回瓦片数据
        Handler-->>Client: 响应瓦片
    else 缓存未命中或需更新
        Cache-->>Handler: 错误或需更新标志
        Handler->>LocalDir: QueryTileFromLocalDirectory(mapReq)

        LocalDir->>LocalDir: getLocalDirectoryProviderType(provider)
        LocalDir->>LocalDir: GetMapProviderTokens(userRid, localProvider)

        loop 遍历每个Token
            LocalDir->>LangMatch: isLanguageCompatible(requestLang, tokenLang, provider, mapType)
            LangMatch-->>LocalDir: 兼容性结果
        end

        alt 找到兼容Token
            LocalDir->>FileReader: readLocalDirectoryFile(token, mapReq)

            alt 文件存在
                FileReader-->>LocalDir: 文件数据
                LocalDir-->>Handler: 瓦片数据
                Handler->>Cache: SaveTileToDb(mapReq, imageBytes)
                Handler-->>Client: 响应瓦片
            else 文件不存在
                FileReader-->>LocalDir: 文件不存在错误
                LocalDir-->>Handler: 错误
                Handler->>NATS: NATS请求
            end
        else 无兼容Token
            LocalDir-->>Handler: 无兼容Token错误
            Handler->>NATS: NATS请求
        end

        alt NATS成功
            NATS-->>Handler: 瓦片数据
            Handler-->>Client: 响应瓦片
        else NATS失败
            Handler->>Online: QueryTileFromProvider(mapReq)
            Online-->>Handler: 瓦片数据
            Handler-->>Client: 响应瓦片
        end
    end
```

### 语言匹配流程图

```mermaid
flowchart TD
    Start[开始语言匹配] --> CheckEmpty{Token语言为空?}
    CheckEmpty -->|是| Compatible[兼容 - 向后兼容]
    CheckEmpty -->|否| CheckSatellite{卫星图层?}

    CheckSatellite -->|是| Compatible
    CheckSatellite -->|否| CheckProvider{Provider类型}

    CheckProvider -->|Google| GoogleMatch[Google语言匹配]
    CheckProvider -->|天地图| TiandituMatch[天地图语言匹配]
    CheckProvider -->|OSM| Compatible

    GoogleMatch --> NormalizeReq[标准化请求语言]
    NormalizeReq --> NormalizeToken[标准化Token语言]
    NormalizeToken --> ExactMatch{精确匹配?}
    ExactMatch -->|是| Compatible
    ExactMatch -->|否| FamilyMatch{语言族匹配?}
    FamilyMatch -->|是| Compatible
    FamilyMatch -->|否| Incompatible[不兼容]

    TiandituMatch --> NormalizeChinese[中文标准化]
    NormalizeChinese --> CheckChinese{都是zh-CN?}
    CheckChinese -->|是| Compatible
    CheckChinese -->|否| Incompatible

    Compatible --> End[返回兼容]
    Incompatible --> End[返回不兼容]
```

## 方法调用关系图

### 核心方法调用链

```mermaid
graph TD
    MapHttpHandler --> QueryTileFromKeyValueDb
    QueryTileFromKeyValueDb -->|失败| QueryTileFromLocalDirectory

    QueryTileFromLocalDirectory --> getLocalDirectoryProviderType
    QueryTileFromLocalDirectory --> GetMapProviderTokens
    QueryTileFromLocalDirectory --> FilterCompatibleTokens

    FilterCompatibleTokens --> isLanguageCompatible
    isLanguageCompatible --> normalizeLanguageCode
    isLanguageCompatible --> ProviderSpecificRules

    ProviderSpecificRules --> GoogleLanguageRule
    ProviderSpecificRules --> TiandituLanguageRule
    ProviderSpecificRules --> OSMLanguageRule

    FilterCompatibleTokens --> ReqMapFromProviderWithToken
    ReqMapFromProviderWithToken --> readLocalDirectoryFile
    readLocalDirectoryFile --> buildLocalFilePath
    readLocalDirectoryFile --> os.ReadFile

    QueryTileFromLocalDirectory -->|成功| SaveTileToDb
    QueryTileFromLocalDirectory -->|失败| NATSRequest
    NATSRequest -->|失败| QueryTileFromProvider
```

### 特殊方法实现

#### handleWithCacheOrOSMMapTile方法修改

```mermaid
flowchart TD
    Start[handleWithCacheOrOSMMapTile] --> CheckSysExpired{sysexpired=1?}
    CheckSysExpired -->|否| QueryCache[QueryTileFromKeyValueDb]
    CheckSysExpired -->|是| QueryCache

    QueryCache -->|成功| Return[返回瓦片]
    QueryCache -->|失败| CheckSysExpiredAgain{sysexpired=1?}

    CheckSysExpiredAgain -->|是| LocalDirFallback[本地目录Fallback]
    CheckSysExpiredAgain -->|否| OSMFallback[OSM Fallback]

    LocalDirFallback -->|成功| Return
    LocalDirFallback -->|失败| OSMFallback

    OSMFallback --> Return
```

#### natsHandlerForMapReq方法扩展

```mermaid
flowchart TD
    Start[natsHandlerForMapReq] --> ParseMsg[解析NATS消息]
    ParseMsg --> QueryCache[QueryTileFromKeyValueDb]

    QueryCache -->|成功| Respond[响应瓦片数据]
    QueryCache -->|失败| LocalDirFallback[本地目录Fallback]

    LocalDirFallback -->|成功| Respond
    LocalDirFallback -->|失败| LogError[记录错误日志]

    LogError --> End[结束处理]
    Respond --> End
```

## 错误处理设计

### 错误处理层次结构

```mermaid
graph TD
    RequestError[请求错误] --> LanguageError[语言处理错误]
    RequestError --> FileError[文件访问错误]
    RequestError --> TokenError[Token错误]

    LanguageError --> InvalidLangCode[无效语言代码]
    LanguageError --> NoCompatibleToken[无兼容Token]

    FileError --> FileNotFound[文件不存在]
    FileError --> PermissionDenied[权限拒绝]
    FileError --> IOError[IO错误]

    TokenError --> TokenExpired[Token过期]
    TokenError --> TokenInvalid[Token无效]
    TokenError --> QuotaExceeded[配额超限]

    InvalidLangCode --> LogWarning[记录警告日志]
    NoCompatibleToken --> FallbackNATS[Fallback到NATS]
    FileNotFound --> LogDebug[记录调试日志]
    PermissionDenied --> LogError[记录错误日志]
    IOError --> LogError
    TokenExpired --> SkipToken[跳过Token]
    TokenInvalid --> SkipToken
    QuotaExceeded --> SkipToken

    LogWarning --> ContinueProcessing[继续处理]
    FallbackNATS --> ContinueProcessing
    LogDebug --> FallbackNATS
    LogError --> FallbackNATS
    SkipToken --> TryNextToken[尝试下一个Token]
```

## 性能优化设计

### 缓存策略

```mermaid
graph LR
    subgraph "内存缓存层"
        LangCache[语言匹配结果缓存]
        TokenCache[Token过滤结果缓存]
        FilePathCache[文件路径缓存]
    end

    subgraph "文件系统缓存"
        OSCache[操作系统文件缓存]
        AppCache[应用级文件缓存]
    end

    Request[请求] --> LangCache
    LangCache -->|命中| FastPath[快速路径]
    LangCache -->|未命中| TokenCache
    TokenCache --> FilePathCache
    FilePathCache --> OSCache
    OSCache --> AppCache
    AppCache --> FileSystem[文件系统]
```

### 并发处理设计

```mermaid
graph TD
    ConcurrentReq[并发请求] --> ReqGroup[请求分组<br/>singleflight.Group]
    ReqGroup --> UniqueReq[去重处理]
    UniqueReq --> LangMatch[语言匹配<br/>并发安全]
    LangMatch --> FileRead[文件读取<br/>并发限制]
    FileRead --> Response[响应分发]
```

## 配置管理设计

### 配置结构

```mermaid
classDiagram
    class LocalDirectoryConfig {
        +EnableLocalDirectory bool
        +MaxConcurrentReads int
        +FileReadTimeout time.Duration
        +CacheLanguageResults bool
        +LanguageCacheTTL time.Duration
    }

    class ProviderConfig {
        +GoogleConfig GoogleProviderConfig
        +TiandituConfig TiandituProviderConfig
        +OSMConfig OSMProviderConfig
    }

    class GoogleProviderConfig {
        +SupportedLanguages []string
        +DefaultLanguage string
        +SkipLanguageForSatellite bool
    }

    LocalDirectoryConfig --> ProviderConfig
    ProviderConfig --> GoogleProviderConfig
```

## 实现细节

### 语言标准化实现

#### normalizeLanguageCode函数

```go
func normalizeLanguageCode(lang string) (string, error) {
    if lang == "" {
        return "", nil
    }

    // 解析语言标签
    tag, err := language.Parse(lang)
    if err != nil {
        return "", fmt.Errorf("invalid language code: %w", err)
    }

    // 标准化为BCP 47格式
    return tag.String(), nil
}
```

**设计说明**：

- 使用`golang.org/x/text/language`库进行语言代码标准化
- 支持BCP 47格式的语言标签
- 处理各种语言变体，依赖标准库的BCP 47格式处理
- 对于只有语言没有国家的语言标签（如`zh`），自动归一化到对应的BCP 47语言格式

### 语言匹配实现

#### isLanguageCompatible函数

```go
func isLanguageCompatible(requestLang, tokenLang string, providerType dbproto.MapProviderEnum, mapType string) bool {
    // 向后兼容：空token语言匹配所有请求
    if tokenLang == "" {
        return true
    }

    // 卫星图跳过语言检查
    if mapType == MapTypeSatellite {
        return true
    }

    // 根据provider类型执行特定匹配逻辑
    switch providerType {
    case dbproto.MapProviderEnum_ProviderGoogleLocalDirectory:
        return matchLanguage(requestLang, tokenLang)
    case dbproto.MapProviderEnum_ProviderTiandituLocalDirectory:
        return matchTiandituLanguage(requestLang, tokenLang)
    case dbproto.MapProviderEnum_ProviderOSMLocalDirectory:
        return true
    default:
        return false
    }
}
```

#### matchLanguage函数

```go
func matchLanguage(requestLang, tokenLang string) bool {
    // 标准化语言代码
    reqLang, err := normalizeLanguageCode(requestLang)
    if err != nil {
        return false
    }

    tokLang, err := normalizeLanguageCode(tokenLang)
    if err != nil {
        return false
    }

    // 精确匹配
    if reqLang == tokLang {
        return true
    }

    // 语言族匹配
    reqTag, err := language.Parse(reqLang)
    if err != nil {
        return false
    }

    tokTag, err := language.Parse(tokLang)
    if err != nil {
        return false
    }

    // 比较语言族（去除地区信息）
    reqBase, _ := reqTag.Base()
    tokBase, _ := tokTag.Base()

    return reqBase == tokBase
}
```

### Provider类型映射实现

#### getLocalDirectoryProviderType函数

```go
func getLocalDirectoryProviderType(onlineProviderType int) int {
    switch onlineProviderType {
    case int(dbproto.MapProviderEnum_ProviderGoogle):
        return int(dbproto.MapProviderEnum_ProviderGoogleLocalDirectory)
    case int(dbproto.MapProviderEnum_ProviderTianditu):
        return int(dbproto.MapProviderEnum_ProviderTiandituLocalDirectory)
    case int(dbproto.MapProviderEnum_ProviderOSM):
        return int(dbproto.MapProviderEnum_ProviderOSMLocalDirectory)
    default:
        return -1 // 不支持的provider类型
    }
}
```

### QueryTileFromLocalDirectory方法实现

```go
func QueryTileFromLocalDirectory(
    userRid string,
    mapReq *MapReq,
    localProviderType int,
    needCreateNewIndex bool,
) (tileInfo *TileInfo, imageBytes []byte, err error) {
    // 获取provider tokens后，添加语言匹配过滤
    providerTokens, err := GlobalMapsManager.GetMapProviderTokens(userRid, localProviderType)
    if err != nil {
        return nil, nil, err
    }

    // 过滤出语言兼容的tokens
    var compatibleTokens []*MapProviderToken
    for _, token := range providerTokens {
        if isLanguageCompatible(mapReq.Lang, token.Language, token.Provider, mapReq.MapType) {
            compatibleTokens = append(compatibleTokens, token)
        }
    }

    if len(compatibleTokens) == 0 {
        return nil, nil, errors.New("no language-compatible local directory provider token found")
    }

    // 尝试从兼容的tokens中读取文件
    for _, token := range compatibleTokens {
        imageBytes, err = readLocalDirectoryFile(token, mapReq)
        if err != nil {
            continue
        }

        // 成功读取文件，保存到数据库并返回
        tileInfo, err = SaveTileToDb(mapReq, imageBytes, needCreateNewIndex)
        return tileInfo, imageBytes, err
    }

    return nil, nil, fmt.Errorf("no local directory file found for request")
}
```

### ReqMapFromProviderWithToken方法扩展

```go
switch providerToken.Provider {
case dbproto.MapProviderEnum_ProviderGoogle:
    // ... 现有Google逻辑 ...
case dbproto.MapProviderEnum_ProviderTianditu:
    // ... 现有Tianditu逻辑 ...
case dbproto.MapProviderEnum_ProviderOSM:
    // ... 现有OSM逻辑 ...

// 新增本地目录provider支持
case dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
     dbproto.MapProviderEnum_ProviderTiandituLocalDirectory,
     dbproto.MapProviderEnum_ProviderOSMLocalDirectory:
    imageBytes, err = readLocalDirectoryFile(providerToken, mapReq)

default:
    return nil, nil, errors.New("not support provider")
}
```

## 总结

本设计文档详细描述了本地目录fallback机制与语言支持功能的完整架构设计。设计的核心特点包括：

1. **模块化设计**：清晰的模块划分，便于维护和扩展
2. **高性能**：多层缓存策略和并发优化
3. **高可靠性**：完善的错误处理和fallback机制
4. **可监控性**：全面的监控指标和日志设计
5. **可扩展性**：支持分布式部署和水平扩展
6. **标准化语言处理**：使用golang.org/x/text/language库的BCP 47标准处理，无需额外的变体格式匹配

该设计为后续的代码实现提供了清晰的指导，确保功能的正确性和系统的稳定性。
